import 'package:flutter/material.dart';
import 'package:geolocator/geolocator.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:package_info_plus/package_info_plus.dart';
import '../l10n/app_localizations.dart';
import '../../features/settings/presentation/bloc/settings_bloc.dart';
import '../../features/location/data/datasources/location_datasource.dart';

class LocationPermissionService {
  static const String _permissionAskedKey = 'location_permission_asked';
  static const String _permissionGrantedKey = 'location_permission_granted';
  static const String _appVersionKey = 'app_version_permissions_checked';
  
  /// Check if location permission request should be shown at startup
  static Future<bool> shouldRequestPermissionAtStartup() async {
    final prefs = await SharedPreferences.getInstance();

    // Get current app version dynamically
    final packageInfo = await PackageInfo.fromPlatform();
    final currentAppVersion = '${packageInfo.version}+${packageInfo.buildNumber}';
    final lastCheckedVersion = prefs.getString(_appVersionKey);
    final isNewInstallOrUpdate = lastCheckedVersion != currentAppVersion;

    // Always check current permission status
    final permission = await Geolocator.checkPermission();

    // Request permission if:
    // 1. New installation or app update
    // 2. Permission is denied or not determined
    // 3. We haven't asked before for this version
    if (isNewInstallOrUpdate) {
      // Reset permission flags for new version
      await prefs.remove(_permissionAskedKey);
      await prefs.remove(_permissionGrantedKey);
      await prefs.setString(_appVersionKey, currentAppVersion);
      return true;
    }

    final hasAsked = prefs.getBool(_permissionAskedKey) ?? false;

    // If we haven't asked before, we should ask
    if (!hasAsked) {
      return true;
    }

    // If permission is denied or not determined, ask again
    return permission == LocationPermission.denied ||
           permission == LocationPermission.deniedForever ||
           permission == LocationPermission.unableToDetermine;
  }
  
  /// Request location permission at app startup with user-friendly dialog
  static Future<bool> requestLocationPermissionAtStartup(BuildContext context) async {
    final l10n = AppLocalizations.of(context)!;
    
    // Show explanation dialog first
    final shouldRequest = await showDialog<bool>(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            Icon(
              Icons.location_on,
              color: Theme.of(context).colorScheme.primary,
            ),
            const SizedBox(width: 8),
            Text(l10n.locationPermissionTitle),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              l10n.locationPermissionDescription,
              style: Theme.of(context).textTheme.bodyMedium,
            ),
            const SizedBox(height: 16),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.primaryContainer,
                borderRadius: BorderRadius.circular(8),
              ),
              child: Column(
                children: [
                  Row(
                    children: [
                      Icon(
                        Icons.schedule,
                        size: 20,
                        color: Theme.of(context).colorScheme.onPrimaryContainer,
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          l10n.locationPermissionBenefit1,
                          style: Theme.of(context).textTheme.bodySmall?.copyWith(
                            color: Theme.of(context).colorScheme.onPrimaryContainer,
                          ),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  Row(
                    children: [
                      Icon(
                        Icons.explore,
                        size: 20,
                        color: Theme.of(context).colorScheme.onPrimaryContainer,
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          l10n.locationPermissionBenefit2,
                          style: Theme.of(context).textTheme.bodySmall?.copyWith(
                            color: Theme.of(context).colorScheme.onPrimaryContainer,
                          ),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
            const SizedBox(height: 12),
            Text(
              l10n.locationPermissionOptional,
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: Theme.of(context).colorScheme.onSurfaceVariant,
                fontStyle: FontStyle.italic,
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: Text(l10n.notNow),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(true),
            child: Text(l10n.allowLocation),
          ),
        ],
      ),
    );
    
    if (shouldRequest != true) {
      // User declined, mark as asked
      await _markPermissionAsked(granted: false);
      return false;
    }
    
    // Request the actual permission
    final permission = await Geolocator.requestPermission();
    final granted = permission == LocationPermission.always ||
                   permission == LocationPermission.whileInUse;
    
    // Store permission result
    await _markPermissionAsked(granted: granted);
    
    // If permission granted, automatically get and save location
    if (granted && context.mounted) {
      try {
        await _getAndSaveCurrentLocation(context);
        _showSuccessDialog(context, l10n);
      } catch (e) {
        // If location detection fails, still show success dialog
        // as permission was granted
        _showSuccessDialog(context, l10n);
      }
    } else {
      _showManualSettingsDialog(context, l10n);
    }
    
    return granted;
  }
  
  /// Check current location permission status
  static Future<bool> hasLocationPermission() async {
    final permission = await Geolocator.checkPermission();
    return permission == LocationPermission.always ||
           permission == LocationPermission.whileInUse;
  }
  
  /// Automatically get and save current GPS location
  static Future<void> _getAndSaveCurrentLocation(BuildContext context) async {
    try {
      // Check if location services are enabled
      bool serviceEnabled = await Geolocator.isLocationServiceEnabled();
      if (!serviceEnabled) {
        return; // Don't throw error, just return
      }

      // Get current position
      Position position = await Geolocator.getCurrentPosition(
        desiredAccuracy: LocationAccuracy.high,
        timeLimit: const Duration(seconds: 10),
      );

      // Get location details using the datasource
      final locationDataSource = LocationDataSourceImpl();
      final locationEntity = await locationDataSource.getLocationFromCoordinates(
        position.latitude,
        position.longitude,
      );

      // Save to settings using BLoC if context is still mounted
      if (context.mounted) {
        context.read<SettingsBloc>().add(
          UpdateCustomLocation(
            latitude: position.latitude,
            longitude: position.longitude,
            locationName: locationEntity.city,
          ),
        );
      }
    } catch (e) {
      // Silently handle errors - don't show error to user
      // as this is an automatic background operation
      debugPrint('Auto location detection failed: $e');
    }
  }

  /// Mark that permission has been asked and store result
  static Future<void> _markPermissionAsked({required bool granted}) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool(_permissionAskedKey, true);
    await prefs.setBool(_permissionGrantedKey, granted);
  }

  /// Reset permission flags (useful for testing or after app reinstall)
  static Future<void> resetPermissionFlags() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(_permissionAskedKey);
    await prefs.remove(_permissionGrantedKey);
  }
  
  /// Show success dialog when permission is granted
  static void _showSuccessDialog(BuildContext context, AppLocalizations l10n) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            Icon(
              Icons.check_circle,
              color: Colors.green,
            ),
            const SizedBox(width: 8),
            Text(l10n.locationPermissionGranted),
          ],
        ),
        content: Text(l10n.locationPermissionGrantedDescription),
        actions: [
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text(l10n.ok),
          ),
        ],
      ),
    );
  }
  
  /// Show dialog suggesting manual settings when permission is denied
  static void _showManualSettingsDialog(BuildContext context, AppLocalizations l10n) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            Icon(
              Icons.info_outline,
              color: Theme.of(context).colorScheme.primary,
            ),
            const SizedBox(width: 8),
            Text(l10n.locationPermissionDenied),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(l10n.locationPermissionDeniedDescription),
            const SizedBox(height: 12),
            Text(
              l10n.locationPermissionManualSettings,
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: Theme.of(context).colorScheme.onSurfaceVariant,
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text(l10n.ok),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              Geolocator.openAppSettings();
            },
            child: Text(l10n.openSettings),
          ),
        ],
      ),
    );
  }
}