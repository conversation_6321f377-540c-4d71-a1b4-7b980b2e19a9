import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../../core/app_config.dart';
import '../../../../core/l10n/app_localizations.dart';
import '../../../../core/utils/hijri_date.dart';
import '../../../../core/widgets/glassmorphism_card.dart';
import '../../../settings/presentation/bloc/settings_bloc.dart';
import '../bloc/prayer_times_bloc.dart';
import '../../domain/entities/prayer_times.dart';
import '../bloc/prayer_times_bloc.dart';

class DateHeader extends StatelessWidget {
  final DateTime date;

  const DateHeader({
    super.key,
    required this.date,
  });

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<SettingsBloc, SettingsState>(
      builder: (context, state) {
        // Wait for settings to load before showing the date
        if (state.isLoading) {
          return const SizedBox(
            height: 80,
            child: Center(child: CircularProgressIndicator()),
          );
        }

        final languageCode = state.language.split('_')[0];
        final hijriOffset = state.hijriOffset;
        final todayLocal = HijriDateUtils.getTodayLocal();
        final isToday = date.year == todayLocal.year && 
                       date.month == todayLocal.month && 
                       date.day == todayLocal.day;
        
        // Use the provided date instead of always using today
        final displayDate = date;
        final gregorianDate = HijriDateUtils.getGregorianDateString(displayDate, languageCode);
        final hijriDate = HijriDateUtils.getHijriDateString(displayDate, languageCode, offset: hijriOffset);
        final isRamadan = HijriDateUtils.isRamadan(displayDate, hijriOffset);

        return GestureDetector(
          onTap: () => _showCalendarDialog(context),
          child: GlassmorphismCard(
            margin: const EdgeInsets.only(bottom: AppConfig.defaultPadding),
            child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header with calendar icon and date type indicator
              Row(
                children: [
                  Icon(
                    Icons.calendar_today,
                    color: isToday 
                        ? Theme.of(context).colorScheme.primary
                        : Theme.of(context).colorScheme.secondary,
                    size: 20,
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      isToday ? 'Today' : _getDateTypeText(displayDate, todayLocal, languageCode),
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.w600,
                        color: isToday 
                            ? Theme.of(context).colorScheme.primary
                            : Theme.of(context).colorScheme.secondary,
                      ),
                    ),
                  ),
                  // Visual indicator for selected date
                  if (!isToday)
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                      decoration: BoxDecoration(
                        color: Theme.of(context).colorScheme.secondary.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(
                          color: Theme.of(context).colorScheme.secondary.withOpacity(0.3),
                          width: 1,
                        ),
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Icon(
                            Icons.event,
                            color: Theme.of(context).colorScheme.secondary,
                            size: 14,
                          ),
                          const SizedBox(width: 4),
                          Text(
                            'Selected',
                            style: Theme.of(context).textTheme.bodySmall?.copyWith(
                              color: Theme.of(context).colorScheme.secondary,
                              fontWeight: FontWeight.w600,
                              fontSize: 10,
                            ),
                          ),
                        ],
                      ),
                    ),
                  if (isRamadan)
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 6),
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          colors: [
                            AppConfig.primaryGold,
                            AppConfig.primaryGold.withOpacity(0.8),
                          ],
                        ),
                        borderRadius: BorderRadius.circular(16),
                        boxShadow: [
                          BoxShadow(
                            color: AppConfig.primaryGold.withOpacity(0.3),
                            blurRadius: 4,
                            offset: const Offset(0, 2),
                          ),
                        ],
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          const Icon(
                            Icons.star,
                            color: Colors.white,
                            size: 14,
                          ),
                          const SizedBox(width: 4),
                          Text(
                            _getRamadanText(languageCode),
                            style: Theme.of(context).textTheme.bodySmall?.copyWith(
                              color: Colors.white,
                              fontWeight: FontWeight.w700,
                              fontSize: 11,
                            ),
                          ),
                        ],
                      ),
                    ),
                ],
              ),
              const SizedBox(height: 12),

              // Gregorian date with enhanced styling for selected dates
              Row(
                children: [
                  Container(
                    width: 4,
                    height: 20,
                    decoration: BoxDecoration(
                      color: isToday 
                          ? Theme.of(context).colorScheme.primary
                          : Theme.of(context).colorScheme.secondary,
                      borderRadius: BorderRadius.circular(2),
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Text(
                      gregorianDate,
                      style: Theme.of(context).textTheme.titleLarge?.copyWith(
                        fontWeight: FontWeight.w600,
                        color: isToday 
                            ? Theme.of(context).colorScheme.primary
                            : Theme.of(context).colorScheme.secondary,
                      ),
                    ),
                  ),
                  // Additional visual feedback for non-today dates
                  if (!isToday)
                    Icon(
                      Icons.schedule,
                      color: Theme.of(context).colorScheme.secondary.withOpacity(0.7),
                      size: 16,
                    ),
                ],
              ),
              const SizedBox(height: 8),

              // Hijri date with consistent styling
              Row(
                children: [
                  Container(
                    width: 4,
                    height: 20,
                    decoration: BoxDecoration(
                      color: AppConfig.primaryGold,
                      borderRadius: BorderRadius.circular(2),
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Text(
                      hijriDate,
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        color: AppConfig.primaryGold,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ],
              ),

              // Ramadan blessing text
              if (isRamadan) ...[
                const SizedBox(height: 12),
                Container(
                  width: double.infinity,
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: AppConfig.primaryGold.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(
                      color: AppConfig.primaryGold.withOpacity(0.3),
                      width: 1,
                    ),
                  ),
                  child: Row(
                    children: [
                      Icon(
                        Icons.mosque,
                        color: AppConfig.primaryGold,
                        size: 18,
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          HijriDateUtils.getRamadanBlessingText(languageCode),
                          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                            color: AppConfig.primaryGold,
                            fontStyle: FontStyle.italic,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ],
          ),
        ),
        );
      },
    );
  }

  void _showCalendarDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Select Date'),
          content: SizedBox(
            width: 300,
            height: 400,
            child: CalendarDatePicker(
              initialDate: date, // Use current displayed date as initial date
              firstDate: DateTime(2020),
              lastDate: DateTime(2030),
              onDateChanged: (DateTime selectedDate) {
                Navigator.of(context).pop();
                _showPrayerTimesPopup(context, selectedDate);
              },
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Cancel'),
            ),
          ],
        );
      },
    );
  }



  void _loadPrayerTimesForDate(BuildContext context, DateTime selectedDate) {
    // Get the settings bloc to access location and calculation method
    final settingsBloc = context.read<SettingsBloc>();
    final settingsState = settingsBloc.state;

    // Get coordinates from settings or use default (Mecca)
    final latitude = settingsState.customLatitude ?? AppConfig.defaultLatitude;
    final longitude = settingsState.customLongitude ?? AppConfig.defaultLongitude;

    // Load prayer times for the selected date
    context.read<PrayerTimesBloc>().add(LoadPrayerTimes(
      date: selectedDate,
      latitude: latitude,
      longitude: longitude,
      calculationMethod: settingsState.calculationMethod,
    ));

    // Show enhanced feedback for selected dates
    final isToday = selectedDate.year == DateTime.now().year &&
                   selectedDate.month == DateTime.now().month &&
                   selectedDate.day == DateTime.now().day;

    final message = isToday
        ? 'Loading today\'s prayer times'
        : 'Loading prayer times for ${selectedDate.day}/${selectedDate.month}/${selectedDate.year}';

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            Icon(
              isToday ? Icons.today : Icons.event,
              color: Colors.white,
              size: 20,
            ),
            const SizedBox(width: 8),
            Expanded(child: Text(message)),
          ],
        ),
        duration: const Duration(seconds: 2),
        backgroundColor: isToday
            ? Theme.of(context).colorScheme.primary
            : Theme.of(context).colorScheme.secondary,
      ),
    );
  }

  String _getDateTypeText(DateTime displayDate, DateTime today, String languageCode) {
    final difference = displayDate.difference(today).inDays;
    
    if (difference == 1) {
      switch (languageCode) {
        case 'de': return 'Morgen';
        case 'hr': return 'Sutra';
        case 'fr': return 'Demain';
        case 'ar': return 'غداً';
        case 'es': return 'Mañana';
        default: return 'Tomorrow';
      }
    } else if (difference == -1) {
      switch (languageCode) {
        case 'de': return 'Gestern';
        case 'hr': return 'Jučer';
        case 'fr': return 'Hier';
        case 'ar': return 'أمس';
        case 'es': return 'Ayer';
        default: return 'Yesterday';
      }
    } else if (difference > 1) {
      switch (languageCode) {
        case 'de': return 'In $difference Tagen';
        case 'hr': return 'Za $difference dana';
        case 'fr': return 'Dans $difference jours';
        case 'ar': return 'بعد $difference أيام';
        case 'es': return 'En $difference días';
        default: return 'In $difference days';
      }
    } else if (difference < -1) {
      final absDiff = difference.abs();
      switch (languageCode) {
        case 'de': return 'Vor $absDiff Tagen';
        case 'hr': return 'Prije $absDiff dana';
        case 'fr': return 'Il y a $absDiff jours';
        case 'ar': return 'منذ $absDiff أيام';
        case 'es': return 'Hace $absDiff días';
        default: return '$absDiff days ago';
      }
    }
    
    return 'Selected Date';
  }

  String _getRamadanText(String languageCode) {
    switch (languageCode) {
      case 'de':
        return 'Ramadan';
      case 'hr':
        return 'Ramazan';
      case 'fr':
        return 'Ramadan';
      case 'ar':
        return 'رمضان';
      default:
        return 'Ramadan';
    }
  }

  void _showPrayerTimesPopup(BuildContext context, DateTime selectedDate) async {
    // Get the settings to access location and calculation method
    final settingsBloc = context.read<SettingsBloc>();
    final settingsState = settingsBloc.state;

    // Get coordinates from settings or use default (Mecca)
    final latitude = settingsState.customLatitude ?? AppConfig.defaultLatitude;
    final longitude = settingsState.customLongitude ?? AppConfig.defaultLongitude;

    // Show loading dialog first
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => const AlertDialog(
        content: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            CircularProgressIndicator(),
            SizedBox(width: 16),
            Text('Loading prayer times...'),
          ],
        ),
      ),
    );

    try {
      // Create a temporary bloc to load prayer times for the selected date
      final prayerTimesBloc = context.read<PrayerTimesBloc>();

      // Create a completer to wait for the prayer times to load
      final completer = Completer<PrayerTimes>();
      late StreamSubscription subscription;

      subscription = prayerTimesBloc.stream.listen((state) {
        if (state is PrayerTimesLoaded) {
          // Check if this is for the selected date
          final loadedDate = DateTime(state.prayerTimes.date.year, state.prayerTimes.date.month, state.prayerTimes.date.day);
          final targetDate = DateTime(selectedDate.year, selectedDate.month, selectedDate.day);

          if (loadedDate.isAtSameMomentAs(targetDate)) {
            subscription.cancel();
            completer.complete(state.prayerTimes);
          }
        } else if (state is PrayerTimesError) {
          subscription.cancel();
          completer.completeError(state.message);
        }
      });

      // Load prayer times for the selected date
      prayerTimesBloc.add(LoadPrayerTimes(
        date: selectedDate,
        latitude: latitude,
        longitude: longitude,
        calculationMethod: settingsState.calculationMethod,
      ));

      // Wait for prayer times to load (with timeout)
      final prayerTimes = await completer.future.timeout(
        const Duration(seconds: 10),
        onTimeout: () {
          subscription.cancel();
          throw TimeoutException('Loading prayer times timed out', const Duration(seconds: 10));
        },
      );

      // Close loading dialog
      if (context.mounted) {
        Navigator.of(context).pop();

        // Show prayer times dialog
        _displayPrayerTimesDialog(context, prayerTimes, selectedDate);
      }

    } catch (e) {
      // Close loading dialog
      if (context.mounted) {
        Navigator.of(context).pop();

        // Show error message
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                const Icon(Icons.error, color: Colors.white),
                const SizedBox(width: 8),
                Text('Failed to load prayer times: ${e.toString()}'),
              ],
            ),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _displayPrayerTimesDialog(BuildContext context, PrayerTimes prayerTimes, DateTime selectedDate) {
    final l10n = AppLocalizations.of(context)!;
    final isToday = selectedDate.year == DateTime.now().year &&
                   selectedDate.month == DateTime.now().month &&
                   selectedDate.day == DateTime.now().day;

    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Row(
            children: [
              Icon(
                isToday ? Icons.today : Icons.event,
                color: Theme.of(context).colorScheme.primary,
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  isToday
                    ? l10n.todaysPrayerTimes
                    : 'Prayer Times - ${selectedDate.day}/${selectedDate.month}/${selectedDate.year}',
                  style: Theme.of(context).textTheme.titleLarge,
                ),
              ),
            ],
          ),
          content: SizedBox(
            width: double.maxFinite,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: prayerTimes.prayersList.map((prayer) => Card(
                margin: const EdgeInsets.symmetric(vertical: 4),
                child: ListTile(
                  leading: Icon(
                    _getPrayerIcon(prayer.name),
                    color: Theme.of(context).colorScheme.primary,
                  ),
                  title: Text(
                    _getPrayerDisplayName(prayer.name, l10n),
                    style: Theme.of(context).textTheme.titleMedium,
                  ),
                  trailing: Text(
                    prayer.time,
                    style: Theme.of(context).textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: Theme.of(context).colorScheme.primary,
                    ),
                  ),
                ),
              )).toList(),
            ),
          ),
          actions: [
            if (!isToday)
              TextButton.icon(
                onPressed: () {
                  Navigator.of(context).pop();
                  // Load this date on main screen
                  _loadPrayerTimesForDate(context, selectedDate);
                },
                icon: const Icon(Icons.visibility),
                label: const Text('Show on Main'),
              ),
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Close'),
            ),
          ],
        );
      },
    );
  }

  IconData _getPrayerIcon(String prayerName) {
    switch (prayerName.toLowerCase()) {
      case 'fajr':
        return Icons.wb_twilight;
      case 'dhuhr':
        return Icons.wb_sunny;
      case 'asr':
        return Icons.wb_sunny_outlined;
      case 'maghrib':
        return Icons.wb_twilight;
      case 'isha':
        return Icons.nightlight_round;
      default:
        return Icons.access_time;
    }
  }

  String _getPrayerDisplayName(String prayerName, AppLocalizations l10n) {
    switch (prayerName.toLowerCase()) {
      case 'fajr':
        return l10n.fajr;
      case 'dhuhr':
        return l10n.dhuhr;
      case 'asr':
        return l10n.asr;
      case 'maghrib':
        return l10n.maghrib;
      case 'isha':
        return l10n.isha;
      default:
        return prayerName;
    }
  }
}
